export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum UserRole {
    ADMIN = "admin",
    MANAGER = "manager",
    SALES_REP = "sales_rep",
    USER = "user"
}
export interface Customer {
    id: string;
    companyName: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: Address;
    industry: string;
    status: CustomerStatus;
    assignedTo: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum CustomerStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    PROSPECT = "prospect",
    LEAD = "lead"
}
export interface Contact {
    id: string;
    customerId: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    position: string;
    isPrimary: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface Lead {
    id: string;
    title: string;
    description: string;
    value: number;
    status: LeadStatus;
    priority: Priority;
    source: string;
    customerId?: string;
    assignedTo: string;
    expectedCloseDate: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum LeadStatus {
    NEW = "new",
    CONTACTED = "contacted",
    QUALIFIED = "qualified",
    PROPOSAL = "proposal",
    NEGOTIATION = "negotiation",
    CLOSED_WON = "closed_won",
    CLOSED_LOST = "closed_lost"
}
export declare enum Priority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export interface Task {
    id: string;
    title: string;
    description: string;
    status: TaskStatus;
    priority: Priority;
    assignedTo: string;
    relatedTo?: {
        type: 'customer' | 'lead' | 'contact';
        id: string;
    };
    dueDate: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum TaskStatus {
    TODO = "todo",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export interface Address {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface LoginResponse {
    user: User;
    token: string;
    refreshToken: string;
}
export interface CreateCustomerRequest {
    companyName: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: Address;
    industry: string;
    assignedTo: string;
}
export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {
    status?: CustomerStatus;
}
export interface CreateLeadRequest {
    title: string;
    description: string;
    value: number;
    priority: Priority;
    source: string;
    customerId?: string;
    expectedCloseDate: Date;
}
export interface UpdateLeadRequest extends Partial<CreateLeadRequest> {
    status?: LeadStatus;
}
export interface CreateTaskRequest {
    title: string;
    description: string;
    priority: Priority;
    assignedTo: string;
    relatedTo?: {
        type: 'customer' | 'lead' | 'contact';
        id: string;
    };
    dueDate: Date;
}
export interface UpdateTaskRequest extends Partial<CreateTaskRequest> {
    status?: TaskStatus;
}
//# sourceMappingURL=index.d.ts.map